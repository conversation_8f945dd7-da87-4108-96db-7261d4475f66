# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 Development Commands

```bash
# Start all applications in development
bun dev

# Start specific applications
bun dev:web        # Frontend only (React Router + Vite)
bun dev:server     # Backend only (Next.js + tRPC)

# Build and type checking
bun build          # Build all applications
bun check-types    # TypeScript validation across monorepo

# Database operations
bun db:push        # Push Prisma schema to PostgreSQL
bun db:studio      # Open Prisma Studio UI
bun db:generate    # Generate Prisma client
bun db:migrate     # Run database migrations
```

## 🏗️ Architecture Overview

This is a **Better-T-Stack** monorepo with TypeScript-first full-stack architecture:

### Frontend (`apps/web/`)
- **React Router 7** with file-based routing (`src/routes/`)
- **Vite** as build tool with TailwindCSS 4
- **shadcn/ui** components in `src/components/ui/`
- **tRPC client** for type-safe API calls to server
- **TanStack Query** for state management and caching

### Backend (`apps/server/`)
- **Next.js 15** with Turbopack for API routes
- **tRPC** server providing type-safe APIs at `/trpc` endpoint
- **Prisma ORM** with PostgreSQL database
- **Custom tRPC context** in `src/lib/context.ts`

### Key Integration Points
- tRPC router defined in `apps/server/src/routers/index.ts`
- Frontend imports server types via `AppRouter` from server
- Database schema in `apps/server/prisma/schema/schema.prisma`
- Prisma client generated to `apps/server/prisma/generated/`

## 🔧 Configuration Notes

### Environment Variables
- **Web app**: `VITE_SERVER_URL` (points to server)
- **Server**: `DATABASE_URL`, `CORS_ORIGIN`
- Environment files: `.env.example` files show required vars

### Database Setup
- PostgreSQL required for development
- Prisma schema uses ESM output format
- Database operations run from server workspace only

### TypeScript Setup
- Monorepo uses workspace TypeScript configurations
- React Router has built-in type generation (`react-router typegen`)
- tRPC provides end-to-end type safety between apps

## 🎯 Development Workflow

1. **Database First**: Set up PostgreSQL and run `bun db:push`
2. **Environment Setup**: Copy `.env.example` files and configure
3. **Development**: Use `bun dev` to run both frontend (port 5173) and backend (port 3000)
4. **Type Safety**: Run `bun check-types` before commits

## 🔍 Review System Architecture

This is a **community-driven blockchain project evaluation platform** with sophisticated review mechanics:

### Review Components
- **ReviewForm** (`apps/web/src/components/review-form.tsx`): Main review submission interface
- **DimensionReview** (`apps/web/src/components/dimension-review.tsx`): Individual dimension evaluation with thumbs voting
- **ThumbsVote** (`apps/web/src/components/thumbs-vote.tsx`): Up/down voting component
- **Review Constants** (`apps/web/src/lib/review-constants.ts`): 6 dimensions with specific questions each

### AI Integration
- **OpenRouter API** with Gemini 2.0 Flash for feedback validation and project analysis
- **Structured validation** checking relevance and quality of user feedback
- **AI Project Analysis** generating comprehensive scores across 6 dimensions
- **Fallback mechanisms** when AI service unavailable
- **Validation scoring** displayed via ValidationBadge component
- **Batch analysis** capabilities for processing multiple projects

### Review Data Flow
1. **User submits** review with dimension scores and feedback
2. **AI validates** feedback relevance using OpenRouter/Gemini
3. **Database stores** validated responses with confidence scores
4. **Spider chart** visualizes aggregated community scores
5. **One review per user per project** constraint enforced

## 📁 Critical File Locations

### Core Architecture
- **tRPC Router**: `apps/server/src/routers/index.ts`
- **tRPC Client Setup**: `apps/web/src/utils/trpc.ts`
- **Database Schema**: `apps/server/prisma/schema/schema.prisma`
- **UI Components**: `apps/web/src/components/ui/`
- **Route Pages**: `apps/web/src/routes/`

### Review System
- **Review Router**: `apps/server/src/routers/reviews.ts`
- **Project Router**: `apps/server/src/routers/projects.ts`
- **AI Analysis Router**: `apps/server/src/routers/ai-analysis.ts`
- **Review Form**: `apps/web/src/components/review-form.tsx`
- **Dimension Questions**: `apps/web/src/lib/review-constants.ts`
- **Spider Chart**: `apps/web/src/components/spider-chart.tsx`
- **AI Analysis Component**: `apps/web/src/components/ai-analysis.tsx`
- **AI Admin Panel**: `apps/web/src/components/ai-admin-panel.tsx`