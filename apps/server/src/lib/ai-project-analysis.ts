import { ReviewDimension } from "../../../web/src/lib/review-constants";

interface ProjectData {
  title: string;
  description: string;
  websiteUrl?: string | null;
  deckUrl?: string | null;
  whitepaperUrl?: string | null;
  socialUrls?: any;
  challengeIntro: string;
}

interface DimensionAnalysis {
  score: number; // 0-100
  reasoning: string;
  strengths: string[];
  weaknesses: string[];
}

interface ProjectAnalysisResult {
  projectFundamentalsScore: number;
  teamGovernanceScore: number;
  transparencyDocScore: number;
  technologyExecutionScore: number;
  communityCommunicationScore: number;
  tokenUtilityTokenomicsScore: number;
  overallScore: number;
  analysis: string;
  reasoning: string;
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
  confidence: number;
}

const ANALYSIS_PROMPT = `
You are an expert blockchain project analyst. Analyze the following project across 6 key dimensions and provide detailed scoring and insights.

Project Information:
- Title: {title}
- Description: {description}
- Challenge/Intro: {challengeIntro}
- Website: {websiteUrl}
- Deck: {deckUrl}
- Whitepaper: {whitepaperUrl}
- Social URLs: {socialUrls}

Analyze this project across these 6 dimensions:

1. **Project Fundamentals** (Narrative Strength + GTM Strategy)
   - Product vision clarity
   - Originality/differentiation
   - Market demand/pain point
   - Compelling narrative
   - Memeable concept
   - Viral traction potential

2. **Team & Governance**
   - Founder experience
   - Team identity verification
   - Functional coverage
   - Governance transparency
   - Advisor credibility
   - Decentralization roadmap

3. **Transparency & Documentation**
   - Website quality
   - Whitepaper/documentation
   - Risk disclosures
   - Business model explanation
   - Roadmap realism
   - Compliance transparency

4. **Technology & Execution**
   - MVP/prototype availability
   - Code repository activity
   - Security audits
   - Tech stack appropriateness
   - Architecture scalability
   - Development progress

5. **Community & Communication**
   - Organic community growth
   - Consistent communication
   - Professional engagement
   - Feedback mechanisms
   - Brand cohesiveness
   - Media leverage

6. **Token Utility & Tokenomics**
   - Token protocol integration
   - Fair allocation structure
   - Sustainability alignment
   - Reasonable vesting
   - User retention incentives
   - Value accrual design

Provide your analysis in the following JSON format:
{
  "projectFundamentalsScore": 0-100,
  "teamGovernanceScore": 0-100,
  "transparencyDocScore": 0-100,
  "technologyExecutionScore": 0-100,
  "communityCommunicationScore": 0-100,
  "tokenUtilityTokenomicsScore": 0-100,
  "overallScore": 0-100,
  "analysis": "Overall project analysis summary (2-3 paragraphs)",
  "reasoning": "Detailed reasoning for scores across all dimensions",
  "strengths": ["strength1", "strength2", "strength3"],
  "weaknesses": ["weakness1", "weakness2", "weakness3"],
  "recommendations": ["recommendation1", "recommendation2", "recommendation3"],
  "confidence": 0-100
}

Be thorough, objective, and provide actionable insights. Base scores on available information and clearly note when information is limited.
`;

export async function analyzeProjectWithAI(projectData: ProjectData): Promise<ProjectAnalysisResult> {
  console.log("🤖 Starting AI analysis for project:", projectData.title);
  
  try {
    const prompt = ANALYSIS_PROMPT
      .replace('{title}', projectData.title)
      .replace('{description}', projectData.description)
      .replace('{challengeIntro}', projectData.challengeIntro)
      .replace('{websiteUrl}', projectData.websiteUrl || 'Not provided')
      .replace('{deckUrl}', projectData.deckUrl || 'Not provided')
      .replace('{whitepaperUrl}', projectData.whitepaperUrl || 'Not provided')
      .replace('{socialUrls}', JSON.stringify(projectData.socialUrls) || 'Not provided');

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.SITE_URL || 'http://localhost:3000',
        'X-Title': 'IBC-CIE Project Analysis'
      },
      body: JSON.stringify({
        model: 'google/gemini-2.0-flash-exp:free',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 2000,
        response_format: { type: "json_object" }
      })
    });

    if (!response.ok) {
      console.error("❌ OpenRouter API error:", response.status, response.statusText);
      throw new Error(`OpenRouter API error: ${response.status}`);
    }

    const data = await response.json();
    console.log("✅ AI analysis response received");

    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      throw new Error('Invalid response format from OpenRouter API');
    }

    const analysisText = data.choices[0].message.content;
    let analysisResult: ProjectAnalysisResult;

    try {
      analysisResult = JSON.parse(analysisText);
      console.log("✅ AI analysis parsed successfully");
    } catch (parseError) {
      console.error("❌ Failed to parse AI analysis JSON:", parseError);
      throw new Error('Failed to parse AI analysis response');
    }

    // Validate and sanitize scores
    const sanitizeScore = (score: number): number => {
      if (typeof score !== 'number' || isNaN(score)) return 0;
      return Math.max(0, Math.min(100, Math.round(score)));
    };

    const sanitizedResult: ProjectAnalysisResult = {
      projectFundamentalsScore: sanitizeScore(analysisResult.projectFundamentalsScore),
      teamGovernanceScore: sanitizeScore(analysisResult.teamGovernanceScore),
      transparencyDocScore: sanitizeScore(analysisResult.transparencyDocScore),
      technologyExecutionScore: sanitizeScore(analysisResult.technologyExecutionScore),
      communityCommunicationScore: sanitizeScore(analysisResult.communityCommunicationScore),
      tokenUtilityTokenomicsScore: sanitizeScore(analysisResult.tokenUtilityTokenomicsScore),
      overallScore: sanitizeScore(analysisResult.overallScore),
      analysis: analysisResult.analysis || 'Analysis not provided',
      reasoning: analysisResult.reasoning || 'Reasoning not provided',
      strengths: Array.isArray(analysisResult.strengths) ? analysisResult.strengths.slice(0, 5) : [],
      weaknesses: Array.isArray(analysisResult.weaknesses) ? analysisResult.weaknesses.slice(0, 5) : [],
      recommendations: Array.isArray(analysisResult.recommendations) ? analysisResult.recommendations.slice(0, 5) : [],
      confidence: sanitizeScore(analysisResult.confidence)
    };

    console.log("🎯 AI analysis completed with overall score:", sanitizedResult.overallScore);
    return sanitizedResult;

  } catch (error) {
    console.error("❌ AI analysis failed:", error);
    
    // Return fallback analysis
    return {
      projectFundamentalsScore: 50,
      teamGovernanceScore: 50,
      transparencyDocScore: 50,
      technologyExecutionScore: 50,
      communityCommunicationScore: 50,
      tokenUtilityTokenomicsScore: 50,
      overallScore: 50,
      analysis: 'AI analysis temporarily unavailable. Scores are estimated based on available project information.',
      reasoning: 'Unable to perform detailed AI analysis due to service limitations.',
      strengths: ['Project information available'],
      weaknesses: ['Limited analysis due to AI service unavailability'],
      recommendations: ['Retry analysis when AI service is available'],
      confidence: 25
    };
  }
}
